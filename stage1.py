import requests
from typing import Optional


def call_stage1_conversion_api(
    source_code: str,
    target_code: str,
    deployment_error: str,
    migration_name: str = 'Oracle_Postgres14',
    project_id: int = 1235,
    tgt_object_id: int = 12345,
    dr_connection_id: int = 123,
    target_connection_id: Optional[int] = 456,
    target_schema_name: str = "public",
    object_type: str = "procedure",
    objectname: str = "test_procedure",
    run_number: int = 1,
    cloud_category: str = "local",
    max_attempt_per_statement: int = 5,
    api_url: str = 'https://dvnext.qmigrator.ai/ai/conversion/conversion-agent',
    auth_token: str = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJuYW1laWQiOiIxMjM1IiwidW5pcXVlX25hbWUiOiIyMCIsInJvbGUiOiJVc2VyIiwibmJmIjoxNzUzODcwNTg1LCJleHAiOjE3NTM5NTY5ODUsImlhdCI6MTc1Mzg3MDU4NSwiaXNzIjoiaHR0cHM6Ly9xbWlncmF0b3IuYWkiLCJhdWQiOiIxMjM1In0.V-JpgCaEy0JE5Cr9M8nBC5wGLwsFsM9WVGmCPxskfeI'
) -> dict:
    """
    Call the Stage 1 Conversion Agent API with the provided parameters.

    Args:
        source_code: Original source database code
        target_code: Target database code with deployment errors
        deployment_error: Error message from target database deployment attempt
        migration_name: Name of the migration (e.g., Oracle_Postgres14)
        project_id: Project ID for the migration
        tgt_object_id: Target object ID for the migration
        dr_connection_id: DR database connection ID (required for all objects)
        target_connection_id: Target database connection ID (optional, used for table/partition)
        target_schema_name: Target schema name for the migration
        object_type: Type of the object being migrated
        objectname: Name of the object being migrated
        run_number: Run number for the migration
        cloud_category: Cloud category for the migration ('local' or 'cloud')
        max_attempt_per_statement: Maximum attempts per target statement
        api_url: API endpoint URL
        auth_token: Authorization token

    Returns:
        dict: Response from the API

    Raises:
        Exception: If the API call fails
    """

    # Headers for the request
    headers = {
        'Authorization': f'Bearer {auth_token}'
    }

    # Form data payload (API expects form data, not JSON)
    form_data = {
        "project_id": project_id,
        "migration_name": migration_name,
        "source_code": source_code,
        "target_code": target_code,
        "deployment_error": str(deployment_error),
        "max_attempt_per_statement": max_attempt_per_statement,
        "tgt_object_id": tgt_object_id,
        "dr_connection_id": dr_connection_id,
        "target_schema_name": target_schema_name,
        "object_type": object_type,
        "objectname": objectname,
        "run_number": run_number,
        "cloud_category": cloud_category
    }

    # Add target_connection_id only if provided
    if target_connection_id is not None:
        form_data["target_connection_id"] = target_connection_id

    try:
        print(f"🚀 Calling Stage 1 Conversion API...")
        print(f"   Migration: {migration_name}")
        print(f"   Object: {target_schema_name}.{objectname} ({object_type})")
        print(f"   API URL: {api_url}")

        # Send form data instead of JSON
        response = requests.post(api_url, data=form_data, headers=headers)
        status_code = response.status_code

        print(f"📊 Status Code: {status_code}")

        if status_code == 200:
            print(f"✅ {migration_name} E2E API successfully triggered")
            print(f"📝 Response: {response.text}")
            return {
                "success": True,
                "status_code": status_code,
                "response": response.text
            }
        else:
            error_msg = f"❌ {migration_name}: Request Failed while calling E2E Migration API"
            print(f"Error Response: {response.text}")
            return {
                "success": False,
                "status_code": status_code,
                "error": error_msg,
                "response": response.text
            }

    except Exception as e:
        error_msg = f"❌ Exception occurred while calling API: {str(e)}"
        print(error_msg)
        return {
            "success": False,
            "error": error_msg,
            "exception": str(e)
        }


# Example usage with sample data
if __name__ == "__main__":
    # Sample data for testing
    sample_source_code = """
CREATE OR REPLACE PROCEDURE test_procedure AS
BEGIN
    SELECT * FROM test_table;
END;
"""

    sample_target_code = """
CREATE OR REPLACE FUNCTION test_procedure() RETURNS VOID AS $$
BEGIN
    SELECT * FROM test_table;
END;
$$ LANGUAGE plpgsql;
"""

    sample_deploy_error = "ERROR: syntax error at or near 'SELECT'"

    # Call the API
    result = call_stage1_conversion_api(
        source_code=sample_source_code,
        target_code=sample_target_code,
        deployment_error=sample_deploy_error,
        migration_name='Oracle_Postgres14',
        project_id=1235,
        tgt_object_id=12345,
        dr_connection_id=123,
        target_connection_id=456,
        target_schema_name="public",
        object_type="procedure",
        objectname="test_procedure",
        run_number=1,
        cloud_category="local"
    )

    if result["success"]:
        print("🎉 API call completed successfully!")
    else:
        print("💥 API call failed!")
        if not result.get("success", True):  # Only raise if we want to stop execution
            raise Exception(result.get("error", "Unknown error occurred"))