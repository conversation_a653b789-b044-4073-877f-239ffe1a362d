import requests
import json




ai_connection_url = 'https://dvnext.qmigrator.ai/'+ 'ai/conversion/conversion-agent'
headers = {'content-type': 'application/json',
           'Authorization': 'Bearer ' + 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJuYW1laWQiOiIxMjM1IiwidW5pcXVlX25hbWUiOiIyMCIsInJvbGUiOiJVc2VyIiwibmJmIjoxNzUzODcwNTg1LCJleHAiOjE3NTM5NTY5ODUsImlhdCI6MTc1Mzg3MDU4NSwiaXNzIjoiaHR0cHM6Ly9xbWlncmF0b3IuYWkiLCJhdWQiOiIxMjM1In0.V-JpgCaEy0JE5Cr9M8nBC5wGLwsFsM9WVGmCPxskfeI'}
 
payload = {
    "project_id": 1235,
    "migration_name": 'Oracle_Postgres14',
    "source_code": original_source_code,
    "target_code": object_converted_output,
    "deployment_error": str(deploy_error),
    "max_attempt_per_statement": 5,
    "tgt_object_id": tgt_id,
    "dr_connection_id": dr_connection_id,
    "target_connection_id": target_connection_id,
    "target_schema_name": target_schema,
    "object_type": object_type,
    "objectname": file_name,
    "run_number": iteration_id,
    "cloud_category": cloud_category
}
response = requests.post(ai_connection_url, data=json.dumps(payload),
                         headers=headers)
status_code = response.status_code
print(status_code)
if status_code == 200:
    print(f"{'Oracle_Postgres14'} E2E API successfully Triggered")
else:
    raise Exception(
        f"{'Oracle_Postgres14'}: Request Failed while calling E2E Migration API")